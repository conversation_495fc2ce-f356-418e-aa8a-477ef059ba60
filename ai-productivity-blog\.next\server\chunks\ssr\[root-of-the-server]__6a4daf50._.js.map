{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Auto%20Blooging%20site/ai-productivity-blog/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ThemeProvider } from \"@/components/theme-provider\";\nimport { Navigation } from \"@/components/navigation\";\nimport { Footer } from \"@/components/footer\";\n\nconst inter = Inter({ subsets: [\"latin\"] });\n\nexport const metadata: Metadata = {\n  title: {\n    default: \"AI Productivity Blog - Your Ultimate Resource for AI Tools & Productivity\",\n    template: \"%s | AI Productivity Blog\"\n  },\n  description: \"Discover the latest AI tools, productivity hacks, and no-code solutions to supercharge your workflow. Expert reviews, tutorials, and automation guides.\",\n  keywords: [\"AI tools\", \"productivity\", \"no-code\", \"automation\", \"SaaS\", \"workflow\", \"business tools\"],\n  authors: [{ name: \"AI Productivity Blog Team\" }],\n  creator: \"AI Productivity Blog\",\n  publisher: \"AI Productivity Blog\",\n  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n    siteName: \"AI Productivity Blog\",\n    title: \"AI Productivity Blog - Your Ultimate Resource for AI Tools & Productivity\",\n    description: \"Discover the latest AI tools, productivity hacks, and no-code solutions to supercharge your workflow.\",\n    images: [\n      {\n        url: \"/og-image.jpg\",\n        width: 1200,\n        height: 630,\n        alt: \"AI Productivity Blog\"\n      }\n    ]\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"AI Productivity Blog - Your Ultimate Resource for AI Tools & Productivity\",\n    description: \"Discover the latest AI tools, productivity hacks, and no-code solutions to supercharge your workflow.\",\n    images: [\"/og-image.jpg\"],\n    creator: \"@aiproductivityblog\"\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n  verification: {\n    google: process.env.GOOGLE_SEARCH_CONSOLE_ID,\n  }\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body className={inter.className}>\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          enableSystem\n          disableTransitionOnChange\n        >\n          <div className=\"min-h-screen flex flex-col\">\n            <Navigation />\n            <main className=\"flex-1\">\n              {children}\n            </main>\n            <Footer />\n          </div>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AASO,MAAM,WAAqB;IAChC,OAAO;QACL,SAAS;QACT,UAAU;IACZ;IACA,aAAa;IACb,UAAU;QAAC;QAAY;QAAgB;QAAW;QAAc;QAAQ;QAAY;KAAiB;IACrG,SAAS;QAAC;YAAE,MAAM;QAA4B;KAAE;IAChD,SAAS;IACT,WAAW;IACX,cAAc,IAAI,IAAI,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IAC1D,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACzC,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAgB;QACzB,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ,QAAQ,GAAG,CAAC,wBAAwB;IAC9C;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC;YAAK,WAAW,yIAAA,CAAA,UAAK,CAAC,SAAS;sBAC9B,cAAA,8OAAC;gBACC,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,yBAAyB;0BAEzB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;;;;sCACD,8OAAC;4BAAK,WAAU;sCACb;;;;;;sCAEH,8OAAC;;;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Auto%20Blooging%20site/ai-productivity-blog/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}