import { createClient } from '@/lib/supabase/server'
import { Database } from '@/lib/supabase/types'

type Article = Database['public']['Tables']['articles']['Row']
type ArticleInsert = Database['public']['Tables']['articles']['Insert']
type ArticleUpdate = Database['public']['Tables']['articles']['Update']

export async function getPublishedArticles(limit = 10, offset = 0) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      categories (
        id,
        name,
        slug,
        color
      ),
      authors (
        id,
        name,
        avatar_url
      )
    `)
    .eq('status', 'published')
    .order('published_at', { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) {
    console.error('Error fetching articles:', error)
    return { articles: [], error }
  }

  return { articles: data, error: null }
}

export async function getArticleBySlug(slug: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      categories (
        id,
        name,
        slug,
        color
      ),
      authors (
        id,
        name,
        bio,
        avatar_url,
        social_links
      ),
      article_tags (
        tags (
          id,
          name,
          slug
        )
      )
    `)
    .eq('slug', slug)
    .eq('status', 'published')
    .single()

  if (error) {
    console.error('Error fetching article:', error)
    return { article: null, error }
  }

  return { article: data, error: null }
}

export async function getArticlesByCategory(categorySlug: string, limit = 10, offset = 0) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      categories!inner (
        id,
        name,
        slug,
        color
      ),
      authors (
        id,
        name,
        avatar_url
      )
    `)
    .eq('categories.slug', categorySlug)
    .eq('status', 'published')
    .order('published_at', { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) {
    console.error('Error fetching articles by category:', error)
    return { articles: [], error }
  }

  return { articles: data, error: null }
}

export async function getArticlesByTag(tagSlug: string, limit = 10, offset = 0) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      categories (
        id,
        name,
        slug,
        color
      ),
      authors (
        id,
        name,
        avatar_url
      ),
      article_tags!inner (
        tags!inner (
          id,
          name,
          slug
        )
      )
    `)
    .eq('article_tags.tags.slug', tagSlug)
    .eq('status', 'published')
    .order('published_at', { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) {
    console.error('Error fetching articles by tag:', error)
    return { articles: [], error }
  }

  return { articles: data, error: null }
}

export async function searchArticles(query: string, limit = 10, offset = 0) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      categories (
        id,
        name,
        slug,
        color
      ),
      authors (
        id,
        name,
        avatar_url
      )
    `)
    .textSearch('title', query)
    .eq('status', 'published')
    .order('published_at', { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) {
    console.error('Error searching articles:', error)
    return { articles: [], error }
  }

  return { articles: data, error: null }
}

export async function createArticle(article: ArticleInsert) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('articles')
    .insert(article)
    .select()
    .single()

  if (error) {
    console.error('Error creating article:', error)
    return { article: null, error }
  }

  return { article: data, error: null }
}

export async function updateArticle(id: string, updates: ArticleUpdate) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('articles')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating article:', error)
    return { article: null, error }
  }

  return { article: data, error: null }
}

export async function incrementViewCount(id: string) {
  const supabase = await createClient()
  
  const { error } = await supabase
    .rpc('increment_view_count', { article_id: id })

  if (error) {
    console.error('Error incrementing view count:', error)
  }
}

export async function getFeaturedArticles(limit = 5) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      categories (
        id,
        name,
        slug,
        color
      ),
      authors (
        id,
        name,
        avatar_url
      )
    `)
    .eq('status', 'published')
    .not('featured_image_url', 'is', null)
    .order('view_count', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error fetching featured articles:', error)
    return { articles: [], error }
  }

  return { articles: data, error: null }
}

export async function getRelatedArticles(articleId: string, categoryId: string, limit = 3) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      categories (
        id,
        name,
        slug,
        color
      ),
      authors (
        id,
        name,
        avatar_url
      )
    `)
    .eq('category_id', categoryId)
    .eq('status', 'published')
    .neq('id', articleId)
    .order('published_at', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error fetching related articles:', error)
    return { articles: [], error }
  }

  return { articles: data, error: null }
}
