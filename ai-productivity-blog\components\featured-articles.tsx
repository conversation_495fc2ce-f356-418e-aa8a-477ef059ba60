import { AnimatedArticleCard } from './animated-article-card'

interface Article {
  id: string
  title: string
  slug: string
  excerpt: string
  featured_image_url: string | null
  category: {
    name: string
    slug: string
    color: string
  }
  author: {
    name: string
    avatar_url: string | null
  }
  published_at: string
  reading_time: number | null
  view_count: number
}

interface FeaturedArticlesProps {
  articles: Article[]
}

export function FeaturedArticles({ articles }: FeaturedArticlesProps) {
  if (!articles.length) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No articles available at the moment.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {articles.map((article, index) => (
        <AnimatedArticleCard
          key={article.id}
          article={article}
          index={index}
        />
      ))}
    </div>
  )
}
