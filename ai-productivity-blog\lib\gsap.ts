import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

// Animation presets
export const animations = {
  // Fade in from bottom
  fadeInUp: (element: string | Element, delay = 0) => {
    return gsap.fromTo(
      element,
      {
        y: 50,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 0.8,
        delay,
        ease: 'power2.out',
      }
    )
  },

  // Fade in from left
  fadeInLeft: (element: string | Element, delay = 0) => {
    return gsap.fromTo(
      element,
      {
        x: -50,
        opacity: 0,
      },
      {
        x: 0,
        opacity: 1,
        duration: 0.8,
        delay,
        ease: 'power2.out',
      }
    )
  },

  // Fade in from right
  fadeInRight: (element: string | Element, delay = 0) => {
    return gsap.fromTo(
      element,
      {
        x: 50,
        opacity: 0,
      },
      {
        x: 0,
        opacity: 1,
        duration: 0.8,
        delay,
        ease: 'power2.out',
      }
    )
  },

  // Scale in animation
  scaleIn: (element: string | Element, delay = 0) => {
    return gsap.fromTo(
      element,
      {
        scale: 0.8,
        opacity: 0,
      },
      {
        scale: 1,
        opacity: 1,
        duration: 0.6,
        delay,
        ease: 'back.out(1.7)',
      }
    )
  },

  // Stagger animation for multiple elements
  staggerFadeInUp: (elements: string | Element[], delay = 0) => {
    return gsap.fromTo(
      elements,
      {
        y: 50,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 0.8,
        delay,
        stagger: 0.1,
        ease: 'power2.out',
      }
    )
  },

  // Hover animations
  hoverScale: (element: string | Element) => {
    const el = typeof element === 'string' ? document.querySelector(element) : element
    if (!el) return

    el.addEventListener('mouseenter', () => {
      gsap.to(el, {
        scale: 1.05,
        duration: 0.3,
        ease: 'power2.out',
      })
    })

    el.addEventListener('mouseleave', () => {
      gsap.to(el, {
        scale: 1,
        duration: 0.3,
        ease: 'power2.out',
      })
    })
  },

  // Parallax effect
  parallax: (element: string | Element, speed = 0.5) => {
    return gsap.to(element, {
      yPercent: -50 * speed,
      ease: 'none',
      scrollTrigger: {
        trigger: element,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
      },
    })
  },

  // Text reveal animation
  textReveal: (element: string | Element, delay = 0) => {
    const el = typeof element === 'string' ? document.querySelector(element) : element
    if (!el) return

    // Split text into spans
    const text = el.textContent || ''
    el.innerHTML = text
      .split('')
      .map((char) => `<span style="display: inline-block;">${char === ' ' ? '&nbsp;' : char}</span>`)
      .join('')

    const chars = el.querySelectorAll('span')

    return gsap.fromTo(
      chars,
      {
        y: 100,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 0.8,
        delay,
        stagger: 0.02,
        ease: 'power2.out',
      }
    )
  },

  // Loading animation
  loading: (element: string | Element) => {
    return gsap.to(element, {
      rotation: 360,
      duration: 1,
      repeat: -1,
      ease: 'none',
    })
  },

  // Page transition
  pageTransition: {
    enter: (element: string | Element) => {
      return gsap.fromTo(
        element,
        {
          opacity: 0,
          y: 20,
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.6,
          ease: 'power2.out',
        }
      )
    },
    exit: (element: string | Element) => {
      return gsap.to(element, {
        opacity: 0,
        y: -20,
        duration: 0.4,
        ease: 'power2.in',
      })
    },
  },
}

// Scroll-triggered animations
export const scrollAnimations = {
  // Fade in when element enters viewport
  fadeInOnScroll: (element: string | Element, options = {}) => {
    return gsap.fromTo(
      element,
      {
        y: 50,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 0.8,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: element,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
          ...options,
        },
      }
    )
  },

  // Counter animation
  counter: (element: string | Element, endValue: number, options = {}) => {
    const obj = { value: 0 }
    return gsap.to(obj, {
      value: endValue,
      duration: 2,
      ease: 'power2.out',
      onUpdate: () => {
        const el = typeof element === 'string' ? document.querySelector(element) : element
        if (el) {
          el.textContent = Math.round(obj.value).toLocaleString()
        }
      },
      scrollTrigger: {
        trigger: element,
        start: 'top 80%',
        toggleActions: 'play none none none',
        ...options,
      },
    })
  },

  // Progress bar animation
  progressBar: (element: string | Element, options = {}) => {
    return gsap.fromTo(
      element,
      {
        scaleX: 0,
      },
      {
        scaleX: 1,
        duration: 1.5,
        ease: 'power2.out',
        transformOrigin: 'left center',
        scrollTrigger: {
          trigger: element,
          start: 'top 80%',
          toggleActions: 'play none none reverse',
          ...options,
        },
      }
    )
  },
}

// Utility functions
export const gsapUtils = {
  // Kill all animations
  killAll: () => {
    gsap.killTweensOf('*')
  },

  // Refresh ScrollTrigger
  refreshScrollTrigger: () => {
    if (typeof window !== 'undefined') {
      ScrollTrigger.refresh()
    }
  },

  // Set default ease
  setDefaults: () => {
    gsap.defaults({
      ease: 'power2.out',
      duration: 0.6,
    })
  },

  // Create timeline
  timeline: (options = {}) => {
    return gsap.timeline(options)
  },
}

export { gsap, ScrollTrigger }
