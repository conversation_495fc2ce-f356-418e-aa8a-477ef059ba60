import { HeroSection } from '@/components/hero-section'
import { FeaturedArticles } from '@/components/featured-articles'
import { CategoryGrid } from '@/components/category-grid'
import { NewsletterSignup } from '@/components/newsletter-signup'

export default async function Home() {
  // Mock data for now - will be replaced with real data from Supabase
  const featuredArticles = [
    {
      id: '1',
      title: 'Top 10 AI Tools That Will Transform Your Productivity in 2025',
      slug: 'top-10-ai-tools-2025',
      excerpt: 'Discover the most powerful AI tools that are revolutionizing how we work, from content creation to data analysis.',
      featured_image_url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop',
      category: { name: 'AI Tools', slug: 'ai-tools', color: '#8B5CF6' },
      author: { name: 'AI Content Generator', avatar_url: null },
      published_at: new Date().toISOString(),
      reading_time: 8,
      view_count: 1250
    },
    {
      id: '2',
      title: 'Building Your First No-Code App: A Complete Guide',
      slug: 'building-first-no-code-app',
      excerpt: 'Learn how to create powerful applications without writing a single line of code using modern no-code platforms.',
      featured_image_url: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=400&fit=crop',
      category: { name: 'No-Code', slug: 'no-code', color: '#10B981' },
      author: { name: 'AI Content Generator', avatar_url: null },
      published_at: new Date(Date.now() - 86400000).toISOString(),
      reading_time: 12,
      view_count: 890
    },
    {
      id: '3',
      title: 'Automation Workflows That Save 10+ Hours Per Week',
      slug: 'automation-workflows-save-time',
      excerpt: 'Discover proven automation strategies that successful entrepreneurs use to reclaim their time and focus on growth.',
      featured_image_url: 'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=800&h=400&fit=crop',
      category: { name: 'Automation', slug: 'automation', color: '#EF4444' },
      author: { name: 'AI Content Generator', avatar_url: null },
      published_at: new Date(Date.now() - 172800000).toISOString(),
      reading_time: 6,
      view_count: 2100
    }
  ]

  const categories = [
    { id: '1', name: 'AI Tools', slug: 'ai-tools', description: 'Latest AI tools and applications', color: '#8B5CF6' },
    { id: '2', name: 'No-Code Solutions', slug: 'no-code', description: 'Build without coding', color: '#10B981' },
    { id: '3', name: 'Productivity', slug: 'productivity', description: 'Boost your efficiency', color: '#F59E0B' },
    { id: '4', name: 'Automation', slug: 'automation', description: 'Automate your workflows', color: '#EF4444' },
    { id: '5', name: 'Reviews', slug: 'reviews', description: 'In-depth tool reviews', color: '#6366F1' }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection featuredArticles={featuredArticles} />

      {/* Featured Articles */}
      <section className="py-16 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Latest Articles
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Stay up-to-date with the latest AI tools, productivity tips, and no-code solutions
            </p>
          </div>
          <FeaturedArticles articles={featuredArticles} />
        </div>
      </section>

      {/* Categories Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Explore Categories
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Discover content tailored to your interests and needs
            </p>
          </div>
          <CategoryGrid categories={categories} />
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-primary/5">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <NewsletterSignup />
        </div>
      </section>
    </div>
  )
}
