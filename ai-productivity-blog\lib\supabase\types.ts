export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      articles: {
        Row: {
          id: string
          title: string
          slug: string
          excerpt: string | null
          content: string
          featured_image_url: string | null
          category_id: string | null
          author_id: string | null
          status: 'draft' | 'published' | 'archived'
          published_at: string | null
          reading_time: number | null
          view_count: number
          seo_title: string | null
          seo_description: string | null
          seo_keywords: string[] | null
          ai_generated: boolean
          ai_prompt: string | null
          source_urls: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          slug: string
          excerpt?: string | null
          content: string
          featured_image_url?: string | null
          category_id?: string | null
          author_id?: string | null
          status?: 'draft' | 'published' | 'archived'
          published_at?: string | null
          reading_time?: number | null
          view_count?: number
          seo_title?: string | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          ai_generated?: boolean
          ai_prompt?: string | null
          source_urls?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          excerpt?: string | null
          content?: string
          featured_image_url?: string | null
          category_id?: string | null
          author_id?: string | null
          status?: 'draft' | 'published' | 'archived'
          published_at?: string | null
          reading_time?: number | null
          view_count?: number
          seo_title?: string | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          ai_generated?: boolean
          ai_prompt?: string | null
          source_urls?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          color: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          color?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          color?: string
          created_at?: string
          updated_at?: string
        }
      }
      authors: {
        Row: {
          id: string
          name: string
          email: string | null
          bio: string | null
          avatar_url: string | null
          social_links: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email?: string | null
          bio?: string | null
          avatar_url?: string | null
          social_links?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string | null
          bio?: string | null
          avatar_url?: string | null
          social_links?: Json
          created_at?: string
          updated_at?: string
        }
      }
      tags: {
        Row: {
          id: string
          name: string
          slug: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          created_at?: string
        }
      }
      article_tags: {
        Row: {
          article_id: string
          tag_id: string
        }
        Insert: {
          article_id: string
          tag_id: string
        }
        Update: {
          article_id?: string
          tag_id?: string
        }
      }
      comments: {
        Row: {
          id: string
          article_id: string
          author_name: string
          author_email: string
          content: string
          status: 'pending' | 'approved' | 'rejected'
          created_at: string
        }
        Insert: {
          id?: string
          article_id: string
          author_name: string
          author_email: string
          content: string
          status?: 'pending' | 'approved' | 'rejected'
          created_at?: string
        }
        Update: {
          id?: string
          article_id?: string
          author_name?: string
          author_email?: string
          content?: string
          status?: 'pending' | 'approved' | 'rejected'
          created_at?: string
        }
      }
      article_analytics: {
        Row: {
          id: string
          article_id: string
          date: string
          views: number
          unique_views: number
          time_on_page: number
          bounce_rate: number
          created_at: string
        }
        Insert: {
          id?: string
          article_id: string
          date: string
          views?: number
          unique_views?: number
          time_on_page?: number
          bounce_rate?: number
          created_at?: string
        }
        Update: {
          id?: string
          article_id?: string
          date?: string
          views?: number
          unique_views?: number
          time_on_page?: number
          bounce_rate?: number
          created_at?: string
        }
      }
      newsletter_subscribers: {
        Row: {
          id: string
          email: string
          name: string | null
          status: 'active' | 'unsubscribed'
          subscribed_at: string
          unsubscribed_at: string | null
        }
        Insert: {
          id?: string
          email: string
          name?: string | null
          status?: 'active' | 'unsubscribed'
          subscribed_at?: string
          unsubscribed_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          status?: 'active' | 'unsubscribed'
          subscribed_at?: string
          unsubscribed_at?: string | null
        }
      }
      ai_automation_logs: {
        Row: {
          id: string
          trigger_type: string
          status: 'running' | 'completed' | 'failed'
          articles_generated: number
          error_message: string | null
          execution_time: number | null
          created_at: string
        }
        Insert: {
          id?: string
          trigger_type: string
          status: 'running' | 'completed' | 'failed'
          articles_generated?: number
          error_message?: string | null
          execution_time?: number | null
          created_at?: string
        }
        Update: {
          id?: string
          trigger_type?: string
          status?: 'running' | 'completed' | 'failed'
          articles_generated?: number
          error_message?: string | null
          execution_time?: number | null
          created_at?: string
        }
      }
      trending_topics: {
        Row: {
          id: string
          topic: string
          search_volume: number | null
          trend_score: number | null
          source: string | null
          category_id: string | null
          processed: boolean
          created_at: string
        }
        Insert: {
          id?: string
          topic: string
          search_volume?: number | null
          trend_score?: number | null
          source?: string | null
          category_id?: string | null
          processed?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          topic?: string
          search_volume?: number | null
          trend_score?: number | null
          source?: string | null
          category_id?: string | null
          processed?: boolean
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
