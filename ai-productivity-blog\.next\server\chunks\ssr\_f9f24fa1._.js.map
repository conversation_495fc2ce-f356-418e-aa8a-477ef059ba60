{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Auto%20Blooging%20site/ai-productivity-blog/src/app/page.tsx"], "sourcesContent": ["import { HeroSection } from '@/components/hero-section'\nimport { FeaturedArticles } from '@/components/featured-articles'\nimport { CategoryGrid } from '@/components/category-grid'\nimport { NewsletterSignup } from '@/components/newsletter-signup'\n\nexport default async function Home() {\n  // Mock data for now - will be replaced with real data from Supabase\n  const featuredArticles = [\n    {\n      id: '1',\n      title: 'Top 10 AI Tools That Will Transform Your Productivity in 2025',\n      slug: 'top-10-ai-tools-2025',\n      excerpt: 'Discover the most powerful AI tools that are revolutionizing how we work, from content creation to data analysis.',\n      featured_image_url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop',\n      category: { name: 'AI Tools', slug: 'ai-tools', color: '#8B5CF6' },\n      author: { name: 'AI Content Generator', avatar_url: null },\n      published_at: new Date().toISOString(),\n      reading_time: 8,\n      view_count: 1250\n    },\n    {\n      id: '2',\n      title: 'Building Your First No-Code App: A Complete Guide',\n      slug: 'building-first-no-code-app',\n      excerpt: 'Learn how to create powerful applications without writing a single line of code using modern no-code platforms.',\n      featured_image_url: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=400&fit=crop',\n      category: { name: 'No-Code', slug: 'no-code', color: '#10B981' },\n      author: { name: 'AI Content Generator', avatar_url: null },\n      published_at: new Date(Date.now() - 86400000).toISOString(),\n      reading_time: 12,\n      view_count: 890\n    },\n    {\n      id: '3',\n      title: 'Automation Workflows That Save 10+ Hours Per Week',\n      slug: 'automation-workflows-save-time',\n      excerpt: 'Discover proven automation strategies that successful entrepreneurs use to reclaim their time and focus on growth.',\n      featured_image_url: 'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=800&h=400&fit=crop',\n      category: { name: 'Automation', slug: 'automation', color: '#EF4444' },\n      author: { name: 'AI Content Generator', avatar_url: null },\n      published_at: new Date(Date.now() - 172800000).toISOString(),\n      reading_time: 6,\n      view_count: 2100\n    }\n  ]\n\n  const categories = [\n    { id: '1', name: 'AI Tools', slug: 'ai-tools', description: 'Latest AI tools and applications', color: '#8B5CF6' },\n    { id: '2', name: 'No-Code Solutions', slug: 'no-code', description: 'Build without coding', color: '#10B981' },\n    { id: '3', name: 'Productivity', slug: 'productivity', description: 'Boost your efficiency', color: '#F59E0B' },\n    { id: '4', name: 'Automation', slug: 'automation', description: 'Automate your workflows', color: '#EF4444' },\n    { id: '5', name: 'Reviews', slug: 'reviews', description: 'In-depth tool reviews', color: '#6366F1' }\n  ]\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <HeroSection featuredArticles={featuredArticles} />\n\n      {/* Featured Articles */}\n      <section className=\"py-16 bg-muted/30\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-foreground mb-4\">\n              Latest Articles\n            </h2>\n            <p className=\"text-muted-foreground max-w-2xl mx-auto\">\n              Stay up-to-date with the latest AI tools, productivity tips, and no-code solutions\n            </p>\n          </div>\n          <FeaturedArticles articles={featuredArticles} />\n        </div>\n      </section>\n\n      {/* Categories Grid */}\n      <section className=\"py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-foreground mb-4\">\n              Explore Categories\n            </h2>\n            <p className=\"text-muted-foreground max-w-2xl mx-auto\">\n              Discover content tailored to your interests and needs\n            </p>\n          </div>\n          <CategoryGrid categories={categories} />\n        </div>\n      </section>\n\n      {/* Newsletter Signup */}\n      <section className=\"py-16 bg-primary/5\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <NewsletterSignup />\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKe,eAAe;IAC5B,oEAAoE;IACpE,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,SAAS;YACT,oBAAoB;YACpB,UAAU;gBAAE,MAAM;gBAAY,MAAM;gBAAY,OAAO;YAAU;YACjE,QAAQ;gBAAE,MAAM;gBAAwB,YAAY;YAAK;YACzD,cAAc,IAAI,OAAO,WAAW;YACpC,cAAc;YACd,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,SAAS;YACT,oBAAoB;YACpB,UAAU;gBAAE,MAAM;gBAAW,MAAM;gBAAW,OAAO;YAAU;YAC/D,QAAQ;gBAAE,MAAM;gBAAwB,YAAY;YAAK;YACzD,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,WAAW;YACzD,cAAc;YACd,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,SAAS;YACT,oBAAoB;YACpB,UAAU;gBAAE,MAAM;gBAAc,MAAM;gBAAc,OAAO;YAAU;YACrE,QAAQ;gBAAE,MAAM;gBAAwB,YAAY;YAAK;YACzD,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,WAAW;YAC1D,cAAc;YACd,YAAY;QACd;KACD;IAED,MAAM,aAAa;QACjB;YAAE,IAAI;YAAK,MAAM;YAAY,MAAM;YAAY,aAAa;YAAoC,OAAO;QAAU;QACjH;YAAE,IAAI;YAAK,MAAM;YAAqB,MAAM;YAAW,aAAa;YAAwB,OAAO;QAAU;QAC7G;YAAE,IAAI;YAAK,MAAM;YAAgB,MAAM;YAAgB,aAAa;YAAyB,OAAO;QAAU;QAC9G;YAAE,IAAI;YAAK,MAAM;YAAc,MAAM;YAAc,aAAa;YAA2B,OAAO;QAAU;QAC5G;YAAE,IAAI;YAAK,MAAM;YAAW,MAAM;YAAW,aAAa;YAAyB,OAAO;QAAU;KACrG;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAY,kBAAkB;;;;;;0BAG/B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAIzD,8OAAC;4BAAiB,UAAU;;;;;;;;;;;;;;;;;0BAKhC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAIzD,8OAAC;4BAAa,YAAY;;;;;;;;;;;;;;;;;0BAK9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;;;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Auto%20Blooging%20site/ai-productivity-blog/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Auto%20Blooging%20site/ai-productivity-blog/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}