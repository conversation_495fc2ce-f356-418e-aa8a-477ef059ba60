"use client"

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronLeft, ChevronRight, Clock, Eye } from 'lucide-react'
import { formatDateRelative, formatNumber } from '@/lib/utils'
import { animations, gsapUtils } from '@/lib/gsap'

interface Article {
  id: string
  title: string
  slug: string
  excerpt: string
  featured_image_url: string | null
  category: {
    name: string
    slug: string
    color: string
  }
  author: {
    name: string
    avatar_url: string | null
  }
  published_at: string
  reading_time: number | null
  view_count: number
}

interface HeroSectionProps {
  featuredArticles: Article[]
}

export function HeroSection({ featuredArticles }: HeroSectionProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const heroRef = useRef<HTMLElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (featuredArticles.length > 1) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % featuredArticles.length)
      }, 5000)
      return () => clearInterval(timer)
    }
  }, [featuredArticles.length])

  // GSAP animations on mount
  useEffect(() => {
    if (contentRef.current && imageRef.current) {
      const tl = gsapUtils.timeline()

      tl.add(animations.fadeInLeft(contentRef.current))
        .add(animations.fadeInRight(imageRef.current), '-=0.4')
    }
  }, [currentSlide])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredArticles.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + featuredArticles.length) % featuredArticles.length)
  }

  if (!featuredArticles.length) {
    return (
      <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              AI Productivity Blog
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Your ultimate resource for AI tools, productivity hacks, and no-code solutions
            </p>
          </div>
        </div>
      </section>
    )
  }

  const currentArticle = featuredArticles[currentSlide]

  return (
    <section ref={heroRef} className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div ref={contentRef} className="space-y-6 opacity-0">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <span 
                  className="px-3 py-1 text-xs font-medium rounded-full text-white"
                  style={{ backgroundColor: currentArticle.category.color }}
                >
                  {currentArticle.category.name}
                </span>
                <span className="text-sm text-muted-foreground">
                  {formatDateRelative(currentArticle.published_at)}
                </span>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-foreground leading-tight">
                {currentArticle.title}
              </h1>
              
              <p className="text-lg text-muted-foreground leading-relaxed">
                {currentArticle.excerpt}
              </p>
            </div>

            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{currentArticle.reading_time} min read</span>
              </div>
              <div className="flex items-center space-x-1">
                <Eye className="w-4 h-4" />
                <span>{formatNumber(currentArticle.view_count)} views</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Link
                href={`/article/${currentArticle.slug}`}
                className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground font-medium rounded-lg hover:bg-primary/90 transition-colors"
              >
                Read Article
              </Link>
              
              <Link
                href={`/category/${currentArticle.category.slug}`}
                className="inline-flex items-center px-6 py-3 border border-border text-foreground font-medium rounded-lg hover:bg-muted transition-colors"
              >
                Explore {currentArticle.category.name}
              </Link>
            </div>

            {/* Slide indicators */}
            {featuredArticles.length > 1 && (
              <div className="flex items-center space-x-2">
                {featuredArticles.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentSlide ? 'bg-primary' : 'bg-muted-foreground/30'
                    }`}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Featured Image */}
          <div ref={imageRef} className="relative opacity-0">
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-muted">
              {currentArticle.featured_image_url ? (
                <Image
                  src={currentArticle.featured_image_url}
                  alt={currentArticle.title}
                  fill
                  className="object-cover"
                  priority
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                  <span className="text-muted-foreground">No image available</span>
                </div>
              )}
            </div>

            {/* Navigation arrows */}
            {featuredArticles.length > 1 && (
              <>
                <button
                  onClick={prevSlide}
                  className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-background/80 backdrop-blur-sm rounded-full flex items-center justify-center text-foreground hover:bg-background transition-colors"
                  aria-label="Previous article"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                
                <button
                  onClick={nextSlide}
                  className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-background/80 backdrop-blur-sm rounded-full flex items-center justify-center text-foreground hover:bg-background transition-colors"
                  aria-label="Next article"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/5 rounded-full blur-3xl" />
      </div>
    </section>
  )
}
