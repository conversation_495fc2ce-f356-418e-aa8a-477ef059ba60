Hello, I'm <PERSON>, and in this video, I'll show you how easy it is
0:03
to build an automated SEO content empire using Lovable and AI agents.
0:09
All of these trends that are trending on Google Trends are now being searched for.
0:14
now I'll publish it to your Lovable site Let's refresh my site
0:17
This is unbelievable.
0:18
And look at the URL up here.
0:20
Nice and SEO friendly. Let's get started.
Building a News Website with a Lovable Prompt
0:23
So here I am with probably the best website and app builder out there
0:26
right now.
0:27
Lovable, where you simply give a natural language prompt and it builds
0:31
the website or web app of your dreams.
0:34
So let's do it.
0:35
Create a modern, clean news website for AI Coding News,
0:40
a platform dedicated to the latest AI developments for developers
0:44
and creators interested in no code and low code solutions.
0:48
This should be a blog style site that gets new articles published to it regularly.
0:52
And there it is, my initial prompt.
0:54
You can follow along with this yourself.
0:56
It's perfect if you're non-technical because no code is needed whatsoever.
1:01
And there you go.
1:01
Straight away.
1:02
It's envisioning a modern, clean news platform.
1:05
Now the idea that's in your head is actually getting created using AI.
1:10
And I just gave one prompt.
1:12
Now if we want to see behind the curtain we can click this.
1:14
And we can watch all the code getting written.
1:16
It looks very, very pretty, but we don't need to know what it does.
1:20
Here it is. AI code daily.
1:22
Doesn't it look incredible?
1:24
Oh my goodness, man.
1:25
It's given some featured sample stories here.
1:28
But don't worry, because we're going to use AI agents to get
1:31
the latest trending information and things that people are searching for.
1:35
So we can publish this regularly on our site.
1:38
If you like what you see so far, throw like and subscribe.
1:40
I do videos like this weekly.
Connecting a Lovable App to a Supabase Database
1:42
So we've got a website spun up and a basic outlook.
1:45
And this has never been done before.
1:46
This has been generated by AI,
1:49
but now we need to get regular articles published on the site,
1:52
and we need to store them in something called a database.
1:55
But don't worry, it's not scary.
1:57
Loveable will do all of this for us.
1:59
So to do this we connect Supabase.
2:01
It's a database but lovable supports brilliantly cleared.
2:05
It gives you a pop up and you can connect a project.
2:08
Now I've connected a couple of organizations already
2:11
and set up my account.
2:12
But if you haven't done so, you just click Create Project here.
2:16
And if you haven't already done
2:17
so, go to Supabase, which I'll link down below and click Start Your Project.
2:21
And then you just need to sign up with an email and password.
2:24
With that done, you'll create an organization.
2:26
and manage your organizations here in Lovable click Add More organizations
2:31
to add in your organization and confirm everything is okay here.
2:35
And authorize Lovable.
2:37
Then we can go ahead and connect a project.
2:38
Go to your organization and create a project.
2:42
I'm just going to make it here on the free plan.
2:44
And we'll call it AI Coding News.
2:48
Then we'll give it a password.
2:49
We can auto generate this.
2:50
We can select somewhere that's close to us and click Create New project.
2:55
You'll see over here it connects my database to Lovable.
2:58
And it even says up here please connect my super base project AI coding news.
3:03
And essentially you'll see that Lovable is working behind
3:06
the scenes to connect your website to a database.
3:09
And look at that within seconds we're connected.
Creating the Automated AI Agent in Zapier Agents
3:11
So now we've connected our website to our database.
3:14
We'll do one more prompt to essentially create a table
3:17
where we store every single story we post on AI coding, news.
3:22
Okay.
3:22
I'd like you to create a table
3:24
that will store all the stories that we publish on AI Code Daily.
3:28
Don't show any story on the website unless it exists in that table.
The Full AI Agent Workflow Breakdown
3:33
Really simple promise.
3:34
Let's run it.
3:35
Now you'll see.
3:35
It's writing a lot of scary data here.
3:38
This is just an SQL command that you can click to apply.
3:42
And when that's done over here
3:44
it should create a table which is listed under the table editor.
3:47
So we'll go back to lovable.
3:49
Click Apply Changes and watch in real time over here as we get a table created.
3:55
Let's refresh.
3:57
Boom.
3:57
Now we've got a table called stories with ID of the story, title,
4:02
excerpt, content, category, author, and so on.
4:06
This is perfect and this is what we'd have done back in the day with WordPress.
4:10
But now we're vibe coding it with natural language inside Lovable.
4:15
And here's our website. Just refresh.
4:16
But this time we've got no example stories
4:18
because we need to publish something in the database.
4:21
And it's super easy. We can do a quick example.
4:23
I can actually insert a row here and just populated with some stuff.
4:28
UUID which is just a custom ID.
4:30
We'll call this.
4:31
This is a test.
4:33
We'll put it in the no code category.
4:36
We'll give my name as the author.
4:38
Then we'll put all the published times.
4:40
This is really cool.
4:41
Eventually, this will be a full article
4:43
on how to create anything without writing a single line of code.
4:47
Nice, and we'll leave the image URL blank for the moment.
4:50
Save it and look that's been added to my database.
4:52
It's successfully created a row, and we'll go back to my website in Lovable
4:57
and look.
4:57
It instantly appears this is a test by Mike Russell.
5:00
Zero hours ago.
5:01
One minute read. How cool is that?
5:04
Now just to double check,
5:05
we can click the preview button here and open it in a new tab.
5:08
And look at our beautiful website.
5:10
This is incredible.
5:12
What would have taken us hours, days and weeks in WordPress
5:15
with lots of plugins
5:16
now takes us just a couple of minutes with vibe coding inside Lovable.
5:20
click on this article. And boom!
5:22
This is a test.
5:23
We are now reading the full article.
5:25
One thing I've noticed here is the URL is not very SEO friendly.
5:28
It's a big ID string there.
5:30
And ideally, it should be the title of the article.
5:33
So back to Lovable.
5:34
We'll say add a column for slug inside my database, and
5:38
make sure the article URL is represented by the slug and not the UUID.
5:44
And we'll let that run.
5:45
And we'll let Lovable sort this out so we get nice SEO friendly URLs.
5:49
You see, again, it wants to execute a command here that will update my database.
5:53
I'll just click Apply Changes down here and let it do it all for me.
5:57
Now you'll see it's actually doing that to make sure it all works.
6:01
If we go over to the database and update it refresh here, we'll see
6:05
that we've got all of our usual fields and then over here.
6:08
Yes. Slug with this is a test has actually been added in for us by Lovable.
6:13
Let's open up a preview.
6:14
Let's click through to our article. And boom!
6:16
Now we have an SEO friendly URL up there.
6:20
I absolutely love this.
6:22
Okay, we've done that in moments.
6:25
Now it's time to connect up an AI agent so we can publish on automation,
6:29
real time stories that are breaking and trending in the moment.
6:34
Now, I'll use Zapier Agents to do this.
6:36
Zapier is the most connected AI orchestration platform out there.
6:39
And look, they even have a free forever plan that allows you to use 400
6:44
agent calls every single month, which is totally insane.
6:48
So over here in agents, I created a pod specifically for my
6:51
AI coding news website and will add an agent will create a new agent
6:56
and you guessed it, in natural language will tell the agent what we want it to do.
7:01
Every morning at 8 a.m., I'd like you to search Google Trends
7:04
for the terms AI, coding and AI tools.
7:07
Then I'd like you to do a Google search for the terms you get back,
7:11
visit websites to learn about this term, and then auto generate
7:15
SEO contents for an AI coding audience interested in no code and low code.
7:21
Store this in an Airtable and then publish to my Lovable news site.
7:26
Okay, that's all we need.
7:27
Initially. Click Create Agent.
7:29
We can always refine the prompt as we go, but.
7:31
This should give us a great start.
7:34
Okay, let's go through our Zapier agent step by step and add in the tools
7:37
needed first.
7:38
Here in the trigger, we'll make sure that is connected
7:41
by selecting the time of day, which for me will be 8 a.m..
7:44
So step one, we're searching Google Trends for the terms AI coding and AI tools.
7:49
Now we're going to use a tool called.
7:50
SerpApi for this, which actually has an integration with Google Trends right here.
7:55
You can choose to register for an account.
7:57
And again the pricing is completely free for 100 searches per month.
8:02
With that done,
8:02
when we're logged into our free plan, we just grab the private API key here.
8:06
And we'll set up step one in our Zapier agent.
8:08
So we'll say using SerpApi.
8:11
And then we'll insert the tool which is going to be from
8:14
many of the thousands of apps we type in webhook here.
8:18
Click that
8:19
and we're going to go for a Get request because we're getting Google Trends data.
8:23
Now we'll go into the Cog.
8:25
And for the URL we'll set a specific field.
8:28
This will be serpapi.com/search.
8:31
Query string params we’ll set again.
8:33
The engine will be Google Trends.
8:37
The Q that's the query will be AI+coding.
8:40
Geo I'll target Geo. I'm going to select US.
8:43
Data type will be related queries.
8:45
Data will be now 7-d which means the last seven days.
8:51
And we finally insert our API key here.
8:54
Next up we set the default for senders Json.
8:57
Json key, we select the default again.
8:59
For Unflatten.
9:00
We select the default again.
9:01
For basic auth we do not include.
9:03
And for headers we do not include.
9:05
I'm going to command C to copy that, and command V to paste it one more time.
9:09
And we'll simply change AI coding to AI tools.
9:12
So we have both queries covered.
9:14
Now the agent can decide exactly how it would like to use those tools.
9:17
Next up we need to identify trending or related search terms
9:21
based on those Google Trends results.
9:23
Now it's very important so we don't get duplicated content
9:26
again and again in our sites.
9:28
I basically cloned my database tables into an air base here,
9:31
and I'll be recording every single article I publish,
9:34
because we can use this as a knowledge base for the agent to check against.
9:38
make sure that this idea doesn't already exist in my Airtable database.
9:43
And then we'll insert a tool here and we'll look for Airtable.
9:46
There it is.
9:47
Now you'll see there's a special icon next to Airtable optimized for AI search,
9:52
meaning the AI agent can read all the contents of our Airtable.
9:56
We'll select that data integration.
9:58
And we'll select a base.
9:59
And that will of course be AI coding news.
10:02
All looks good. So we add that as a source.
10:04
Now you'll see when we do that syncing appears here because I agent will
10:08
now synchronize everything in our Airtable and stay constantly up to date.
10:14
while it's doing that will add more tools which are tools to search with Google.
10:18
This is performing a Google search for those terms.
10:21
Extracting relevant information from top results.
10:24
So we'll give it the ability to visit any website.
10:27
Step five is to auto generate that SEO content.
10:30
We don't need to add a tool there.
10:31
The agent will just do it.
10:33
then we need to add the content to our Airtable.
10:35
So I'll add that ability by searching for Airtable again
10:38
selecting it and clicking Create Record.
10:41
And finally step seven to get us all the way there,
10:44
publish the content from Airtable to your Lovable news site.
10:47
Well guess what?
10:47
Zapier also has an integration with databases,
10:50
so you can get everything up on your website live
10:53
without you even needing to touch anything.
10:56
Your AI agent works around the clock, and I'll show you how.
10:59
Okay.
10:59
Supabase actually has a fantastic tutorial on connecting to Zapier,
11:03
and I'll link that down below, but you can follow along with me now.
11:06
So first we need to go to the rather scary looking SQL editor.
11:09
But don't worry, it's just a simple case of running a few commands.
11:13
So let's click here. New SQL snippet.
11:15
And you'll see the first thing we copy is to create a Zapier user.
11:18
Let's paste that in there and put our super secret password in here.
11:22
Now we click the run button.
11:24
And if it worked okay, you'll see.
11:25
User creation for Zapier. Success.
11:28
No rows returned.
11:29
That's actually a good thing. It was successful.
11:31
We've now created a Zapier user.
11:34
Here we are back in the SQL editor.
11:35
I'm going to copy the first command here, paste it in a couple of changes to make.
11:39
Make sure the table is
11:40
the name of the table we created earlier on in our table editor.
11:44
That is, stories, in our case auto populated.
11:47
And remove the quotes from the Zapier username.
11:50
Click run boom. Success!
11:52
We've now granted the ability for Zapier to write to our stories table.
11:56
Finally, we run. This commands.
11:58
We'll open a new tab, paste it in Zapier,
12:01
can read from stories table.
12:04
And again on Public Stories
12:08
to Zapier.
12:09
True run.
12:11
Success again.
12:13
Now all the permissions have been granted.
12:15
Let's have some fun.
12:16
Before we go to Zapier, we'll just click
12:18
this connect button up here and we'll get all of our database table.
12:22
That'll give us everything we need to connect Zapier to our database.
12:25
Okay. We're so nearly there.
12:26
Finally for step seven we'll just say using PostgreSQL,
12:31
which is actually the name of the database we host over here inside Supabase.
12:35
So finally we'll insert at all and we'll go to the very bottom,
12:39
add a new tool and we'll look for post grabber.
12:42
And it should come up there. It is.
12:43
And we'll be creating new rows here.
12:46
Now we need to connect our database.
12:49
Okay, this is really simple.
12:50
We just grab that URL we copied earlier.
12:52
Grab everything after the symbol, before the colon.
12:55
That's the host port.
12:57
5432 is exactly what it should be.
13:00
Then for the database that's going to be Postgres.
13:02
It's going to be public username.
13:04
That will be Zapier that we created earlier.
13:08
Plus the long string from our URL there.
13:11
And then the password.
13:12
That's our super secure password.
13:14
Finally, under Database settings, we'll need to go to the SSL configuration.
13:17
Download the certificate.
13:19
And paste the contents in here
13:20
at SSL CA bundle to make sure we can connect securely.
13:26
And look at that.
13:27
We are connected. Now we can choose a table.
13:30
And of course, we can set a specific field and choose from our database.
13:33
And you'll see it loads up the stories table.
13:36
So now we know it's always working with that.
13:39
But we can leave everything else such as author, category, content.
13:42
In fact, everything else is decided by our AI agent.
13:46
How cool is that?
13:47
We'll just click save here.
13:49
And now it genuinely looks like everything is ready to go.
13:52
Let's test our AI agent for the first time and see if it can publish
13:55
on automation to our AI coding news blog.
13:59
And to do this I'll just click Test Agent over here and let it run away.
14:02
So we can actually see in real time
Testing the AI Agent Live!
14:04
how the agent will run on automation every day at eight in the morning.
14:08
And first of all, it's preparing to get the information from SerpApi.
14:12
Now you'll see all along the way in test mode it wants me to approve.
14:15
So I'm doing that.
14:16
And you can see a tick here.
14:18
It has actually got the trends all retrieved from Google Trends.
14:22
We can see lots of different AI things coming in here.
14:25
And now it's already deciding to analyze the data sources it's found.
14:29
And look at this.
14:29
All of the trends here OpenAI, AI app.
14:32
All of these trends that are trending on Google Trends are now being searched for.
14:37
It's specifically important that it's looking at our present Airtable
14:40
to see if any of this stuff has been covered.
14:42
And now you'll see immediately it's got to work.
14:44
And it's searching Google.
14:45
It's completed that search.
14:46
And it's found all of these great things about the Oakley AI Smartglasses
14:51
it's thinking about what to do next.
14:53
And now you'll see here it's actually visiting a website
14:56
all about those glasses.
14:57
Now you can see it.
14:58
Already wants to add this to my Airtable, which is great.
15:00
So I'll approve.
15:01
And you'll see over here is just created a record for the Oakley Meta AI glasses.
15:06
That's great.
15:07
And now it's going to my Postgres database which is hosted here.
15:10
We set this up with Lovable.
15:12
And it's going to add that as a row.
15:14
You see it says over here now
15:15
I'll publish it to your Lovable site and it's asking for approval.
15:18
It's now creating that row in my database.
15:21
And we can actually go over and see this in real time happen over here.
15:24
And look at this.
15:25
We've got Oakley Meta AI glasses inserted into our table.
15:29
And over here in our AI agent.
15:31
We've got a tick here it is inserted.
15:33
This story over here. This is amazing.
15:36
And it even says success.
15:37
Today's trending AI news story about Oakley Meta AI glasses
15:41
is now published on your Lovable news site.
15:44
Wow. That's amazing.
15:46
Let's take a look and see how it came out.
15:48
Let's refresh my site and look.
15:49
Yes, we've got a new article here.
The Automated Result: A New SEO Article!
15:52
This is unbelievable.
15:53
Meta and Oakley have launched the Oakley Meta
15:57
AI glasses.
15:58
Wow, that is so, so crazy.
16:02
And look at the URL up here.
16:03
Nice and SEO friendly.
16:05
now. This is just a basic getting started,
16:07
but as I'm sure you can imagine, there's so much more you can do.
Next Steps & Outro
16:10
You can add featured images, links out to the sites
16:13
you use to create that article, and continuous agents
16:17
that run maybe three, four, five times a day putting out content
16:21
on your newly created site that you made in minutes using Lovable, a database,
16:26
and then Zapier agents to populate that database.
16:29
It's incredible what is possible.
16:31
I'll link up the tutorial down below.
16:33
It will be inside my community, and I'm also starting a cohort
16:36
soon to teach you how to make sites and apps just like this.
16:40
So if you're interested in that too, the link will be down below.
16:43
Thank you so much for watching this video, and YouTube is showing a video
16:46
on your screen right now. You should watch next. Thanks.