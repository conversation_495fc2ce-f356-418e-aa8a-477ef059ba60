import Link from 'next/link'
import { <PERSON><PERSON><PERSON>, <PERSON>ap, Code, Cog, Star, Brain } from 'lucide-react'

interface Category {
  id: string
  name: string
  slug: string
  description: string
  color: string
}

interface CategoryGridProps {
  categories: Category[]
}

const categoryIcons: Record<string, React.ComponentType<{ className?: string }>> = {
  'ai-tools': Brain,
  'no-code': Code,
  'productivity': Zap,
  'automation': Cog,
  'reviews': Star,
}

export function CategoryGrid({ categories }: CategoryGridProps) {
  if (!categories.length) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No categories available.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {categories.map((category) => {
        const Icon = categoryIcons[category.slug] || Zap
        
        return (
          <Link
            key={category.id}
            href={`/category/${category.slug}`}
            className="group relative bg-card rounded-xl border border-border p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 overflow-hidden"
          >
            {/* Background decoration */}
            <div 
              className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity"
              style={{ backgroundColor: category.color }}
            />
            
            {/* Icon */}
            <div className="relative mb-4">
              <div 
                className="w-12 h-12 rounded-lg flex items-center justify-center text-white"
                style={{ backgroundColor: category.color }}
              >
                <Icon className="w-6 h-6" />
              </div>
            </div>

            {/* Content */}
            <div className="relative space-y-2">
              <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
                {category.name}
              </h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {category.description}
              </p>
            </div>

            {/* Arrow */}
            <div className="relative mt-4 flex items-center text-sm font-medium text-primary">
              <span>Explore</span>
              <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
            </div>

            {/* Hover effect border */}
            <div 
              className="absolute inset-0 rounded-xl border-2 border-transparent group-hover:border-current transition-colors opacity-0 group-hover:opacity-20"
              style={{ borderColor: category.color }}
            />
          </Link>
        )
      })}
    </div>
  )
}
