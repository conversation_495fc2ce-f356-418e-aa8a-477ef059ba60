"use client"

import { useEffect, useRef } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Clock, Eye, User } from 'lucide-react'
import { formatDateRelative, formatNumber } from '@/lib/utils'
import { scrollAnimations } from '@/lib/gsap'

interface Article {
  id: string
  title: string
  slug: string
  excerpt: string
  featured_image_url: string | null
  category: {
    name: string
    slug: string
    color: string
  }
  author: {
    name: string
    avatar_url: string | null
  }
  published_at: string
  reading_time: number | null
  view_count: number
}

interface AnimatedArticleCardProps {
  article: Article
  index: number
}

export function AnimatedArticleCard({ article, index }: AnimatedArticleCardProps) {
  const cardRef = useRef<HTMLElement>(null)

  useEffect(() => {
    if (cardRef.current) {
      scrollAnimations.fadeInOnScroll(cardRef.current, {
        delay: index * 0.1,
      })
    }
  }, [index])

  return (
    <article
      ref={cardRef}
      className="group bg-card rounded-xl border border-border overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1 opacity-0"
    >
      {/* Featured Image */}
      <div className="relative aspect-[16/10] overflow-hidden">
        {article.featured_image_url ? (
          <Image
            src={article.featured_image_url}
            alt={article.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
            <span className="text-muted-foreground text-sm">No image</span>
          </div>
        )}
        
        {/* Category badge */}
        <div className="absolute top-4 left-4">
          <Link
            href={`/category/${article.category.slug}`}
            className="px-3 py-1 text-xs font-medium rounded-full text-white hover:opacity-90 transition-opacity"
            style={{ backgroundColor: article.category.color }}
          >
            {article.category.name}
          </Link>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-4">
        {/* Title */}
        <h3 className="text-xl font-bold text-foreground leading-tight group-hover:text-primary transition-colors">
          <Link href={`/article/${article.slug}`}>
            {article.title}
          </Link>
        </h3>

        {/* Excerpt */}
        <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3">
          {article.excerpt}
        </p>

        {/* Meta information */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>{article.reading_time} min</span>
            </div>
            <div className="flex items-center space-x-1">
              <Eye className="w-3 h-3" />
              <span>{formatNumber(article.view_count)}</span>
            </div>
          </div>
          <span>{formatDateRelative(article.published_at)}</span>
        </div>

        {/* Author */}
        <div className="flex items-center space-x-2 pt-2 border-t border-border">
          <div className="flex items-center space-x-2">
            {article.author.avatar_url ? (
              <Image
                src={article.author.avatar_url}
                alt={article.author.name}
                width={24}
                height={24}
                className="rounded-full"
              />
            ) : (
              <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center">
                <User className="w-3 h-3 text-muted-foreground" />
              </div>
            )}
            <span className="text-sm text-muted-foreground">
              {article.author.name}
            </span>
          </div>
        </div>
      </div>
    </article>
  )
}
