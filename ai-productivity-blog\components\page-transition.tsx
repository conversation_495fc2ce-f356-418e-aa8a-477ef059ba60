"use client"

import { useEffect, useRef } from 'react'
import { usePathname } from 'next/navigation'
import { animations } from '@/lib/gsap'

interface PageTransitionProps {
  children: React.ReactNode
}

export function PageTransition({ children }: PageTransitionProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const pathname = usePathname()

  useEffect(() => {
    if (containerRef.current) {
      animations.pageTransition.enter(containerRef.current)
    }
  }, [pathname])

  return (
    <div ref={containerRef} className="opacity-0">
      {children}
    </div>
  )
}
