"use client"

import { useState, useEffect } from 'react'
import { 
  FileText, 
  Eye, 
  Users, 
  TrendingUp, 
  Plus,
  MoreHorizontal,
  Calendar,
  Clock
} from 'lucide-react'
import { StatsGrid } from '@/components/animated-counter'
import { formatDateRelative } from '@/lib/utils'

// Mock data - replace with real data from Supabase
const mockStats = [
  { value: 156, label: 'Total Articles', suffix: '' },
  { value: 25400, label: 'Total Views', suffix: '' },
  { value: 1250, label: 'Subscribers', suffix: '' },
  { value: 89, label: 'Growth Rate', suffix: '%' },
]

const mockRecentArticles = [
  {
    id: '1',
    title: 'Top 10 AI Tools That Will Transform Your Productivity in 2025',
    status: 'published',
    views: 1250,
    published_at: new Date().toISOString(),
  },
  {
    id: '2',
    title: 'Building Your First No-Code App: A Complete Guide',
    status: 'draft',
    views: 0,
    published_at: null,
  },
  {
    id: '3',
    title: 'Automation Workflows That Save 10+ Hours Per Week',
    status: 'published',
    views: 2100,
    published_at: new Date(Date.now() - 86400000).toISOString(),
  },
]

const mockRecentComments = [
  {
    id: '1',
    author: '<PERSON> Doe',
    content: 'Great article! Really helped me understand AI tools better.',
    article: 'Top 10 AI Tools...',
    created_at: new Date(Date.now() - 3600000).toISOString(),
  },
  {
    id: '2',
    author: 'Jane Smith',
    content: 'Could you add more examples for the no-code section?',
    article: 'Building Your First...',
    created_at: new Date(Date.now() - 7200000).toISOString(),
  },
]

export function AdminDashboard() {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here's what's happening with your blog.</p>
        </div>
        <button className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
          <Plus className="w-4 h-4" />
          <span>New Article</span>
        </button>
      </div>

      {/* Stats Grid */}
      <div className="bg-card rounded-lg border border-border p-6">
        <h2 className="text-xl font-semibold text-foreground mb-6">Overview</h2>
        <StatsGrid stats={mockStats} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Articles */}
        <div className="bg-card rounded-lg border border-border p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-foreground">Recent Articles</h2>
            <button className="text-primary hover:text-primary/80 text-sm font-medium">
              View All
            </button>
          </div>
          <div className="space-y-4">
            {mockRecentArticles.map((article) => (
              <div key={article.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex-1">
                  <h3 className="font-medium text-foreground text-sm line-clamp-1">
                    {article.title}
                  </h3>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-muted-foreground">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      article.status === 'published' 
                        ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                        : 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300'
                    }`}>
                      {article.status}
                    </span>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-3 h-3" />
                      <span>{article.views}</span>
                    </div>
                    {article.published_at && (
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDateRelative(article.published_at)}</span>
                      </div>
                    )}
                  </div>
                </div>
                <button className="p-1 text-muted-foreground hover:text-foreground">
                  <MoreHorizontal className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Comments */}
        <div className="bg-card rounded-lg border border-border p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-foreground">Recent Comments</h2>
            <button className="text-primary hover:text-primary/80 text-sm font-medium">
              View All
            </button>
          </div>
          <div className="space-y-4">
            {mockRecentComments.map((comment) => (
              <div key={comment.id} className="p-3 bg-muted/50 rounded-lg">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-foreground text-sm">{comment.author}</span>
                      <span className="text-xs text-muted-foreground">
                        on {comment.article}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {comment.content}
                    </p>
                    <div className="flex items-center space-x-1 mt-2 text-xs text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      <span>{formatDateRelative(comment.created_at)}</span>
                    </div>
                  </div>
                  <button className="p-1 text-muted-foreground hover:text-foreground">
                    <MoreHorizontal className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-card rounded-lg border border-border p-6">
        <h2 className="text-xl font-semibold text-foreground mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center space-x-3 p-4 bg-primary/5 border border-primary/20 rounded-lg hover:bg-primary/10 transition-colors">
            <FileText className="w-5 h-5 text-primary" />
            <div className="text-left">
              <div className="font-medium text-foreground">Create Article</div>
              <div className="text-sm text-muted-foreground">Write a new blog post</div>
            </div>
          </button>
          <button className="flex items-center space-x-3 p-4 bg-secondary/5 border border-secondary/20 rounded-lg hover:bg-secondary/10 transition-colors">
            <TrendingUp className="w-5 h-5 text-secondary" />
            <div className="text-left">
              <div className="font-medium text-foreground">View Analytics</div>
              <div className="text-sm text-muted-foreground">Check performance metrics</div>
            </div>
          </button>
          <button className="flex items-center space-x-3 p-4 bg-accent/5 border border-accent/20 rounded-lg hover:bg-accent/10 transition-colors">
            <Users className="w-5 h-5 text-accent" />
            <div className="text-left">
              <div className="font-medium text-foreground">Manage Users</div>
              <div className="text-sm text-muted-foreground">Handle subscribers</div>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
