"use client"

import { useEffect, useRef } from 'react'
import { animations } from '@/lib/gsap'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const spinnerRef = useRef<HTMLDivElement>(null)

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  useEffect(() => {
    if (spinnerRef.current) {
      animations.loading(spinnerRef.current)
    }
  }, [])

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div
        ref={spinnerRef}
        className={`${sizeClasses[size]} border-2 border-primary/20 border-t-primary rounded-full`}
      />
    </div>
  )
}

// Page loading component
export function PageLoader() {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="text-center space-y-4">
        <LoadingSpinner size="lg" />
        <p className="text-muted-foreground">Loading...</p>
      </div>
    </div>
  )
}
