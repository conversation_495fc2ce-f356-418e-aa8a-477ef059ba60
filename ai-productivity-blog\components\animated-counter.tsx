"use client"

import { useEffect, useRef } from 'react'
import { scrollAnimations } from '@/lib/gsap'

interface AnimatedCounterProps {
  endValue: number
  label: string
  suffix?: string
  className?: string
}

export function AnimatedCounter({ 
  endValue, 
  label, 
  suffix = '', 
  className = '' 
}: AnimatedCounterProps) {
  const counterRef = useRef<HTMLSpanElement>(null)

  useEffect(() => {
    if (counterRef.current) {
      scrollAnimations.counter(counterRef.current, endValue)
    }
  }, [endValue])

  return (
    <div className={`text-center ${className}`}>
      <div className="text-3xl md:text-4xl font-bold text-foreground">
        <span ref={counterRef}>0</span>
        {suffix && <span className="text-primary">{suffix}</span>}
      </div>
      <p className="text-muted-foreground mt-2">{label}</p>
    </div>
  )
}

// Stats section component
interface StatsData {
  value: number
  label: string
  suffix?: string
}

interface StatsGridProps {
  stats: StatsData[]
  className?: string
}

export function StatsGrid({ stats, className = '' }: StatsGridProps) {
  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 ${className}`}>
      {stats.map((stat, index) => (
        <AnimatedCounter
          key={index}
          endValue={stat.value}
          label={stat.label}
          suffix={stat.suffix}
        />
      ))}
    </div>
  )
}
