import { createClient } from '@/lib/supabase/server'
import { Database } from '@/lib/supabase/types'

type Category = Database['public']['Tables']['categories']['Row']
type CategoryInsert = Database['public']['Tables']['categories']['Insert']
type CategoryUpdate = Database['public']['Tables']['categories']['Update']

export async function getAllCategories() {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name')

  if (error) {
    console.error('Error fetching categories:', error)
    return { categories: [], error }
  }

  return { categories: data, error: null }
}

export async function getCategoryBySlug(slug: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('slug', slug)
    .single()

  if (error) {
    console.error('Error fetching category:', error)
    return { category: null, error }
  }

  return { category: data, error: null }
}

export async function getCategoriesWithArticleCount() {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .select(`
      *,
      articles!category_id (
        count
      )
    `)
    .order('name')

  if (error) {
    console.error('Error fetching categories with article count:', error)
    return { categories: [], error }
  }

  return { categories: data, error: null }
}

export async function createCategory(category: CategoryInsert) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .insert(category)
    .select()
    .single()

  if (error) {
    console.error('Error creating category:', error)
    return { category: null, error }
  }

  return { category: data, error: null }
}

export async function updateCategory(id: string, updates: CategoryUpdate) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating category:', error)
    return { category: null, error }
  }

  return { category: data, error: null }
}

export async function deleteCategory(id: string) {
  const supabase = await createClient()
  
  const { error } = await supabase
    .from('categories')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting category:', error)
    return { error }
  }

  return { error: null }
}
